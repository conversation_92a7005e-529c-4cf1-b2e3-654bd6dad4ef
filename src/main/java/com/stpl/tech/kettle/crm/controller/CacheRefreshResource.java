package com.stpl.tech.kettle.crm.controller;

import com.stpl.tech.kettle.crm.cache.BrandDetailCache;
import com.stpl.tech.kettle.crm.service.impl.CartRulesRecommendationServiceImpl;
import com.stpl.tech.kettle.crm.util.CustomerServiceConstants;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@RestController
@Log4j2
@RequestMapping(value = CustomerServiceConstants.API_VERSION + CustomerServiceConstants.SEPARATOR + CustomerServiceConstants.CACHE_REFRESH_RESOURCE)
public class CacheRefreshResource {

    @Autowired
    private BrandDetailCache brandDetailCache;

    @Autowired
    private CartRulesRecommendationServiceImpl cartRulesRecommendationService;

    @PostMapping(value = "refresh-brand-cache")
    public boolean refreshBrandDetailCache() {
        try {
            brandDetailCache.removeBrandDetailCache();
            return true;
        }catch (Exception e){
            log.error("Error while removing brand detail cache");
        }
        return false;
    }

    @PostMapping(value = "refresh-input-rules-cache")
    public boolean refreshInputRulesCache() {
        try {
            cartRulesRecommendationService.clearInputRulesCache();
            log.info("Successfully cleared input rules cache");
            return true;
        } catch (Exception e) {
            log.error("Error while clearing input rules cache", e);
        }
        return false;
    }

    @PostMapping(value = "refresh-output-rules-cache")
    public boolean refreshOutputRulesCache() {
        try {
            cartRulesRecommendationService.clearOutputRulesCache();
            log.info("Successfully cleared output rules cache");
            return true;
        } catch (Exception e) {
            log.error("Error while clearing output rules cache", e);
        }
        return false;
    }

    @PostMapping(value = "refresh-all-rules-cache")
    public boolean refreshAllRulesCache() {
        try {
            cartRulesRecommendationService.clearRuleDecisionCache();
            log.info("Successfully cleared all rules cache");
            return true;
        } catch (Exception e) {
            log.error("Error while clearing all rules cache", e);
        }
        return false;
    }

    @PostMapping(value = "get-cache-statistics")
    public Map<String, Object> getCacheStatistics() {
        try {
            return cartRulesRecommendationService.getCacheStatistics();
        } catch (Exception e) {
            log.error("Error while getting cache statistics", e);
            return Map.of("error", "Failed to retrieve cache statistics");
        }
    }
}
