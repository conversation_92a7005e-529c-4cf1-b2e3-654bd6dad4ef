package com.stpl.tech.kettle.crm.service.impl;

import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.stpl.tech.common.cache.ProductCache;
import com.stpl.tech.kettle.crm.cache.CartRulesDecisionCache;
import com.stpl.tech.kettle.crm.cache.UnitDroolVersionMappingCache;
import com.stpl.tech.kettle.crm.cache.impl.CrmCacheService;
import com.stpl.tech.kettle.crm.data.kettle.OrderDetail;
import com.stpl.tech.kettle.crm.exception.DataUpdationException;
import com.stpl.tech.kettle.crm.repository.clm.CustomerFavouriteProductsDao;
import com.stpl.tech.kettle.crm.repository.kettle.OrderDetailDao;
import com.stpl.tech.kettle.crm.service.CartRulesRecommendationService;
import com.stpl.tech.kettle.crm.service.CustomerService;
import com.stpl.tech.kettle.crm.util.AppConstants;
import com.stpl.tech.kettle.crm.util.AppUtils;
import com.stpl.tech.kettle.crm.util.DroolFileType;
import com.stpl.tech.kettle.domain.kettle.model.CartRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.OutputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.RecommendedProduct;
import com.stpl.tech.kettle.domain.kettle.model.recom.CustomerRecommendationDroolProperties;
import com.stpl.tech.kettle.domain.kettle.model.request.CartRulesRecommendationRequest;
import com.stpl.tech.kettle.domain.kettle.model.response.CartRulesRecommendationResponse;
import com.stpl.tech.kettle.domain.master.model.recom.CartRecommendationDayPart;
import com.stpl.tech.master.domain.model.DroolVersionDomain;
import com.stpl.tech.master.readonly.domain.model.ProductVO;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Cart Rules Recommendation Service Implementation
 * Implements cart-based recommendation processing using drool rules
 */
@Service
@Log4j2
public class CartRulesRecommendationServiceImpl implements CartRulesRecommendationService {

    @Autowired
    private CartRulesOrchestrationService cartRulesOrchestrationService;

    @Autowired
    private CustomerFavouriteProductsDao customerFavouriteProductsDao;

    @Autowired
    private ProductCache productCache;

    @Autowired
    private OrderDetailDao orderDetailDao;

    @Autowired
    private UnitDroolVersionMappingCache unitDroolVersionMappingCache;

    @Override
    public CartRulesRecommendationResponse getCartBasedRecommendations(CartRulesRecommendationRequest request) {
        Stopwatch stopwatch = Stopwatch.createUnstarted();
        Map<String, DroolVersionDomain> unitDroolVersionMapping =  unitDroolVersionMappingCache.getUnitDroolVersionMapping(request.getUnitId());
        log.info("Getting Customer Recom Drool Properties for customer ID :{}", request.getCustomerId());
        stopwatch.start();
        CustomerRecommendationDroolProperties customerRecommendationDroolProperties = customerFavouriteProductsDao.getCustomerRecomDroolProperties(request.getCustomerId(), Objects.equals(request.getCustType(), "NEW"));
        log.info("&&&&&& ::: Step 1 : Got Customer Recom Drool Properties for customer ID :{} and its details are :::{} in :{}", request.getCustomerId(), new Gson().toJson(customerRecommendationDroolProperties), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        stopwatch.start();
        log.info("Starting cart-based recommendations for customer: {} at unit: {}",
                request.getCustomerId(), request.getUnitId());

        try {
            // Step 1: Build cart rules decision properties from request
            CartRulesDroolDecisionProperties cartRulesDecision = buildCartRulesDecision(request, customerRecommendationDroolProperties);
            log.info("Built cart rules decision: {}", new Gson().toJson(cartRulesDecision));
            log.info("&&&&&& ::: Step 1 : Got Customer Recom Drool Properties for customer ID :{} and its details are :::{} in :{}", request.getCustomerId(), new Gson().toJson(customerRecommendationDroolProperties), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
            stopwatch.start();

            // Step 2: Execute cart rules and get cart rules result with list of matched rules
            String cartDroolVersion = Objects.nonNull(unitDroolVersionMapping) && unitDroolVersionMapping.containsKey(DroolFileType.CART_RULES_DROOL_DECISION.name()) ?
                    unitDroolVersionMapping.get(DroolFileType.CART_RULES_DROOL_DECISION.name()).getVersion() : null;
            CartRulesDroolDecisionProperties cartRulesDroolDecisionProperties = cartRulesOrchestrationService
                    .executeCartRulesDecision(cartRulesDecision, cartDroolVersion);
            log.info("Executed cart rules decision: {} matched rules", cartRulesDroolDecisionProperties.getInputRulesToBeRun());

            // Step 2.1: Execute cart rules and get cart rules result with list of matched rules
            String inputRulesDroolversion = Objects.nonNull(unitDroolVersionMapping) && unitDroolVersionMapping.containsKey(DroolFileType.OUTPUT_RULES_DROOL_DECISION.name()) ?
                    unitDroolVersionMapping.get(DroolFileType.INPUT_RULES_DROOL_DECISION.name()).getVersion() : null;
            List<InputRulesDroolDecisionProperties> inputRules = cartRulesOrchestrationService
                    .executeInputRulesFromCartResult(cartRulesDroolDecisionProperties, inputRulesDroolversion);
            log.info("Executed {} output rules from cart rules", inputRules.size());


            // Step 3: Execute output rules based on cart rules result

            String outputRulesDroolVersion = Objects.nonNull(unitDroolVersionMapping) && unitDroolVersionMapping.containsKey(DroolFileType.OUTPUT_RULES_DROOL_DECISION.name()) ?
                    unitDroolVersionMapping.get(DroolFileType.OUTPUT_RULES_DROOL_DECISION.name()).getVersion() : null;

            List<OutputRulesDroolDecisionProperties> outputRules = new ArrayList<>();
            if(Objects.nonNull(inputRules)) {
                for (InputRulesDroolDecisionProperties inputRule : inputRules) {
                    List<OutputRulesDroolDecisionProperties> cartOutputRules = cartRulesOrchestrationService
                            .executeOutputRulesFromCartResult(inputRule, outputRulesDroolVersion);
                    log.info("Executed {} output rules from cart rules", outputRules.size());
                    if (Objects.nonNull(cartOutputRules)) {
                        outputRules.addAll(cartOutputRules);
                    }
                }
            }

            // Step 4: Generate recommendations from output rules
            List<RecommendedProduct> recommendedProducts = generateRecommendationsFromRules(outputRules, request);
            log.info("Generated {} recommended products", recommendedProducts.size());

            return CartRulesRecommendationResponse.builder()
                    .cartRulesDecision(cartRulesDecision)
                    .inputRulesDroolDecisionProperties(inputRules)
                    .outputRules(outputRules)
                    .recommendedProducts(recommendedProducts)
                    .build();

        } catch (Exception e) {
            log.error("Error processing cart-based recommendations for customer: {}", request.getCustomerId(), e);
            return buildErrorResponse(request, stopwatch);
        }
    }

    /**
     * Build cart rules decision properties from request
     */
    private CartRulesDroolDecisionProperties buildCartRulesDecision(CartRulesRecommendationRequest request, CustomerRecommendationDroolProperties customerRecommendationDroolProperties) throws DataUpdationException {

        OrderDetail orderDetail = orderDetailDao.findTopByCustomerId(request.getCustomerId(),  AppConstants.SETTLED);
        // Determine time of day if not provided
        String timeOfDay = CartRecommendationDayPart.getCurrentDayPart(AppUtils.getCurrentTimestamp()).name();

        return CartRulesDroolDecisionProperties.builder()
                .lastOrderCategory(Objects.nonNull(customerRecommendationDroolProperties) ? customerRecommendationDroolProperties.getDineInFoodClassPreference() : "DEFAULT")
                .minPreviousCartATV(Objects.nonNull(orderDetail) ? orderDetail.getTaxableAmount().intValue() : AppConstants.ZERO)
                .maxPreviousCartATV(Objects.nonNull(orderDetail) ? orderDetail.getTaxableAmount().intValue() : AppConstants.ZERO)
                .customerFrequency(Objects.nonNull(customerRecommendationDroolProperties) ? customerRecommendationDroolProperties.getCustomerFrequecy() : "DEFAULT")
                .timeOfDay(timeOfDay)
                .customerNumberAvailable(Objects.nonNull(request.getCustomerNumberAvailable()) || Objects.nonNull(orderDetail) ?
                        AppConstants.YES : AppConstants.NO)
                .build();
    }

    /**
     * Generate recommendations from executed output rules
     */
    private List<RecommendedProduct> generateRecommendationsFromRules(
            List<OutputRulesDroolDecisionProperties> outputRules,
            CartRulesRecommendationRequest request) {

        List<RecommendedProduct> allRecommendations = new ArrayList<>();
        Map<Integer, ProductVO> availableProducts = productCache.getProductByUnit(request.getUnitId());

        for (OutputRulesDroolDecisionProperties rule : outputRules) {
            List<RecommendedProduct> ruleRecommendations = generateRecommendationsFromRule(rule, availableProducts);
            allRecommendations.addAll(ruleRecommendations);
        }

        // Remove duplicates and limit results
        return allRecommendations.stream()
                .collect(Collectors.toMap(
                        RecommendedProduct::getProductId,
                        product -> product,
                        (existing, replacement) -> existing))
                .values()
                .stream()
                .limit(20) // Limit to 20 recommendations
                .collect(Collectors.toList());
    }

    /**
     * Generate recommendations from a single output rule
     */
    private List<RecommendedProduct> generateRecommendationsFromRule(
            OutputRulesDroolDecisionProperties rule,
            Map<Integer, ProductVO> availableProducts) {

        List<RecommendedProduct> recommendations = new ArrayList<>();
        List<Integer> productIds = rule.getProductIDsList();

        for (Integer productId : productIds) {
            if (availableProducts.containsKey(productId)) {
                ProductVO product = availableProducts.get(productId);

                RecommendedProduct recommendation = RecommendedProduct.builder()
                        .productId(productId)
                        .categoryId(product.getType())
                        .recomReason(rule.getRecommendationType())
                        .build();

                recommendations.add(recommendation);

                // Limit per rule
                if (recommendations.size() >= rule.getProductCount()) {
                    break;
                }
            }
        }

        return recommendations;
    }

    /**
     * Build error response
     */
    private CartRulesRecommendationResponse buildErrorResponse(
            CartRulesRecommendationRequest request, Stopwatch stopwatch) {

        return CartRulesRecommendationResponse.builder()
                .cartRulesDecision(null)
                .outputRules(List.of())
                .recommendedProducts(List.of())
                .categoryProductMap(Map.of())
                .categorySequence(List.of())
                .appliedDiscounts(List.of())
                .executionMetadata(CartRulesRecommendationResponse.ExecutionMetadata.builder()
                        .executionTimeMs(stopwatch.elapsed(TimeUnit.MILLISECONDS))
                        .droolVersion("error")
                        .totalRulesEvaluated(0)
                        .rulesExecuted(0)
                        .executionTimestamp(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                        .build())
                .build();
    }
}
