package com.stpl.tech.kettle.crm.service.impl;

import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.stpl.tech.common.cache.ProductCache;
import com.stpl.tech.kettle.crm.cache.UnitDroolVersionMappingCache;
import com.stpl.tech.kettle.crm.cache.impl.CrmCacheService;
import com.stpl.tech.kettle.crm.data.kettle.OrderDetail;
import com.stpl.tech.kettle.crm.exception.DataUpdationException;
import com.stpl.tech.kettle.crm.repository.clm.CustomerFavouriteProductsDao;
import com.stpl.tech.kettle.crm.repository.kettle.OrderDetailDao;
import com.stpl.tech.kettle.crm.service.CartRulesRecommendationService;
import com.stpl.tech.kettle.crm.service.CustomerService;
import com.stpl.tech.kettle.crm.util.AppConstants;
import com.stpl.tech.kettle.crm.util.AppUtils;
import com.stpl.tech.kettle.crm.util.DroolFileType;
import com.stpl.tech.kettle.domain.kettle.model.CartRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.OutputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.RecommendedProduct;
import com.stpl.tech.kettle.domain.kettle.model.recom.CustomerRecommendationDroolProperties;
import com.stpl.tech.kettle.domain.kettle.model.request.CartRulesRecommendationRequest;
import com.stpl.tech.kettle.domain.kettle.model.response.CartRulesRecommendationResponse;
import com.stpl.tech.kettle.domain.master.model.recom.CartRecommendationDayPart;
import com.stpl.tech.master.domain.model.DroolVersionDomain;
import com.stpl.tech.master.readonly.domain.model.ProductVO;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Cart Rules Recommendation Service Implementation
 * Implements cart-based recommendation processing using drool rules
 */
@Service
@Log4j2
public class CartRulesRecommendationServiceImpl implements CartRulesRecommendationService {

    @Autowired
    private CartRulesOrchestrationService cartRulesOrchestrationService;

    @Autowired
    private CustomerFavouriteProductsDao customerFavouriteProductsDao;

    @Autowired
    private ProductCache productCache;

    @Autowired
    private OrderDetailDao orderDetailDao;

    @Autowired
    private UnitDroolVersionMappingCache unitDroolVersionMappingCache;

    // Cache for input and output rule decision properties
    // Key format: {ruleId}_{input_rule|output_rule}
    private final Map<String, Object> ruleDecisionCache = new ConcurrentHashMap<>();

    @Override
    public CartRulesRecommendationResponse getCartBasedRecommendations(CartRulesRecommendationRequest request) {
        Stopwatch stopwatch = Stopwatch.createUnstarted();
        Map<String, DroolVersionDomain> unitDroolVersionMapping =  unitDroolVersionMappingCache.getUnitDroolVersionMapping(request.getUnitId());
        log.info("Getting Customer Recom Drool Properties for customer ID :{}", request.getCustomerId());
        stopwatch.start();
        CustomerRecommendationDroolProperties customerRecommendationDroolProperties = customerFavouriteProductsDao.getCustomerRecomDroolProperties(request.getCustomerId(), Objects.equals(request.getCustType(), "NEW"));
        log.info("&&&&&& ::: Step 1 : Got Customer Recom Drool Properties for customer ID :{} and its details are :::{} in :{}", request.getCustomerId(), new Gson().toJson(customerRecommendationDroolProperties), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        stopwatch.start();
        log.info("Starting cart-based recommendations for customer: {} at unit: {}",
                request.getCustomerId(), request.getUnitId());

        try {
            // Step 1: Build cart rules decision properties from request
            CartRulesDroolDecisionProperties cartRulesDecision = buildCartRulesDecision(request, customerRecommendationDroolProperties);
            log.info("Built cart rules decision: {}", new Gson().toJson(cartRulesDecision));
            log.info("&&&&&& ::: Step 1 : Got Customer Recom Drool Properties for customer ID :{} and its details are :::{} in :{}", request.getCustomerId(), new Gson().toJson(customerRecommendationDroolProperties), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
            stopwatch.start();

            // Step 2: Execute cart rules and get cart rules result with list of matched rules
            String cartDroolVersion = Objects.nonNull(unitDroolVersionMapping) && unitDroolVersionMapping.containsKey(DroolFileType.CART_RULES_DROOL_DECISION.name()) ?
                    unitDroolVersionMapping.get(DroolFileType.CART_RULES_DROOL_DECISION.name()).getVersion() : null;
            CartRulesDroolDecisionProperties cartRulesDroolDecisionProperties = cartRulesOrchestrationService
                    .executeCartRulesDecision(cartRulesDecision, cartDroolVersion);
            log.info("Executed cart rules decision: {} matched rules", cartRulesDroolDecisionProperties.getInputRulesToBeRun());

            // Step 2.1: Execute input rules with caching
            String inputRulesDroolversion = Objects.nonNull(unitDroolVersionMapping) && unitDroolVersionMapping.containsKey(DroolFileType.OUTPUT_RULES_DROOL_DECISION.name()) ?
                    unitDroolVersionMapping.get(DroolFileType.INPUT_RULES_DROOL_DECISION.name()).getVersion() : null;
            List<InputRulesDroolDecisionProperties> inputRules = getInputRulesWithCache(cartRulesDroolDecisionProperties, inputRulesDroolversion);
            log.info("Retrieved {} input rules (from cache and/or execution)", inputRules.size());


            // Step 3: Execute output rules based on cart rules result

            String outputRulesDroolVersion = Objects.nonNull(unitDroolVersionMapping) && unitDroolVersionMapping.containsKey(DroolFileType.OUTPUT_RULES_DROOL_DECISION.name()) ?
                    unitDroolVersionMapping.get(DroolFileType.OUTPUT_RULES_DROOL_DECISION.name()).getVersion() : null;

            // Step 3: Execute output rules with caching
            List<OutputRulesDroolDecisionProperties> outputRules = getOutputRulesWithCache(inputRules, outputRulesDroolVersion);
            log.info("Retrieved {} output rules (from cache and/or execution)", outputRules.size());

            // Step 4: Generate recommendations from output rules
            List<RecommendedProduct> recommendedProducts = generateRecommendationsFromRules(outputRules, request);
            log.info("Generated {} recommended products", recommendedProducts.size());

            return CartRulesRecommendationResponse.builder()
                    .cartRulesDecision(cartRulesDecision)
                    .inputRulesDroolDecisionProperties(inputRules)
                    .outputRules(outputRules)
                    .recommendedProducts(recommendedProducts)
                    .build();

        } catch (Exception e) {
            log.error("Error processing cart-based recommendations for customer: {}", request.getCustomerId(), e);
            return buildErrorResponse(request, stopwatch);
        }
    }

    /**
     * Build cart rules decision properties from request
     */
    private CartRulesDroolDecisionProperties buildCartRulesDecision(CartRulesRecommendationRequest request, CustomerRecommendationDroolProperties customerRecommendationDroolProperties) throws DataUpdationException {

        OrderDetail orderDetail = orderDetailDao.findTopByCustomerId(request.getCustomerId(),  AppConstants.SETTLED);
        // Determine time of day if not provided
        String timeOfDay = CartRecommendationDayPart.getCurrentDayPart(AppUtils.getCurrentTimestamp()).name();

        return CartRulesDroolDecisionProperties.builder()
                .lastOrderCategory(Objects.nonNull(customerRecommendationDroolProperties) ? customerRecommendationDroolProperties.getDineInFoodClassPreference() : "DEFAULT")
                .minPreviousCartATV(Objects.nonNull(orderDetail) ? orderDetail.getTaxableAmount().intValue() : AppConstants.ZERO)
                .maxPreviousCartATV(Objects.nonNull(orderDetail) ? orderDetail.getTaxableAmount().intValue() : AppConstants.ZERO)
                .customerFrequency(Objects.nonNull(customerRecommendationDroolProperties) ? customerRecommendationDroolProperties.getCustomerFrequecy() : "DEFAULT")
                .timeOfDay(timeOfDay)
                .customerNumberAvailable(Objects.nonNull(request.getCustomerNumberAvailable()) || Objects.nonNull(orderDetail) ?
                        AppConstants.YES : AppConstants.NO)
                .build();
    }

    /**
     * Generate recommendations from executed output rules
     */
    private List<RecommendedProduct> generateRecommendationsFromRules(
            List<OutputRulesDroolDecisionProperties> outputRules,
            CartRulesRecommendationRequest request) {

        List<RecommendedProduct> allRecommendations = new ArrayList<>();
        Map<Integer, ProductVO> availableProducts = productCache.getProductByUnit(request.getUnitId());

        for (OutputRulesDroolDecisionProperties rule : outputRules) {
            List<RecommendedProduct> ruleRecommendations = generateRecommendationsFromRule(rule, availableProducts);
            allRecommendations.addAll(ruleRecommendations);
        }

        // Remove duplicates and limit results
        return allRecommendations.stream()
                .collect(Collectors.toMap(
                        RecommendedProduct::getProductId,
                        product -> product,
                        (existing, replacement) -> existing))
                .values()
                .stream()
                .limit(20) // Limit to 20 recommendations
                .collect(Collectors.toList());
    }

    /**
     * Generate recommendations from a single output rule
     */
    private List<RecommendedProduct> generateRecommendationsFromRule(
            OutputRulesDroolDecisionProperties rule,
            Map<Integer, ProductVO> availableProducts) {

        List<RecommendedProduct> recommendations = new ArrayList<>();
        List<Integer> productIds = rule.getProductIDsList();

        for (Integer productId : productIds) {
            if (availableProducts.containsKey(productId)) {
                ProductVO product = availableProducts.get(productId);

                RecommendedProduct recommendation = RecommendedProduct.builder()
                        .productId(productId)
                        .categoryId(product.getType())
                        .recomReason(rule.getRecommendationType())
                        .build();

                recommendations.add(recommendation);

                // Limit per rule
                if (recommendations.size() >= rule.getProductCount()) {
                    break;
                }
            }
        }

        return recommendations;
    }

    /**
     * Build error response
     */
    private CartRulesRecommendationResponse buildErrorResponse(
            CartRulesRecommendationRequest request, Stopwatch stopwatch) {

        return CartRulesRecommendationResponse.builder()
                .cartRulesDecision(null)
                .outputRules(List.of())
                .recommendedProducts(List.of())
                .categoryProductMap(Map.of())
                .categorySequence(List.of())
                .appliedDiscounts(List.of())
                .executionMetadata(CartRulesRecommendationResponse.ExecutionMetadata.builder()
                        .executionTimeMs(stopwatch.elapsed(TimeUnit.MILLISECONDS))
                        .droolVersion("error")
                        .totalRulesEvaluated(0)
                        .rulesExecuted(0)
                        .executionTimestamp(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                        .build())
                .build();
    }

    /**
     * Get input rules with caching support
     * Checks cache first, then executes drool rules for missing entries
     */
    private List<InputRulesDroolDecisionProperties> getInputRulesWithCache(
            CartRulesDroolDecisionProperties cartRulesDecision, String inputRulesDroolVersion) {

        List<InputRulesDroolDecisionProperties> result = new ArrayList<>();

        try {
            // Extract rule IDs from cart rules decision
            List<Integer> ruleIds = extractInputRuleIds(cartRulesDecision);
            if (ruleIds.isEmpty()) {
                log.debug("No input rule IDs found in cart rules decision");
                return result;
            }

            List<Integer> missingRuleIds = new ArrayList<>();

            // Check cache for each rule ID
            for (Integer ruleId : ruleIds) {
                String cacheKey = createCacheKey(ruleId, "input_rule");
                Object cachedRule = ruleDecisionCache.get(cacheKey);

                if (cachedRule instanceof InputRulesDroolDecisionProperties) {
                    result.add((InputRulesDroolDecisionProperties) cachedRule);
                    log.debug("Found input rule {} in cache", ruleId);
                } else {
                    missingRuleIds.add(ruleId);
                    log.debug("Input rule {} not found in cache", ruleId);
                }
            }

            // Execute drool rules for missing rules
            if (!missingRuleIds.isEmpty()) {
                log.info("Executing drool rules for {} missing input rules: {}", missingRuleIds.size(), missingRuleIds);
                List<InputRulesDroolDecisionProperties> executedRules = cartRulesOrchestrationService
                        .executeInputRulesFromCartResult(cartRulesDecision, inputRulesDroolVersion);

                // Cache the newly executed rules and add to result
                for (InputRulesDroolDecisionProperties rule : executedRules) {
                    if (Objects.nonNull(rule) && Objects.nonNull(rule.getRuleNum()) &&
                        missingRuleIds.contains(rule.getRuleNum())) {

                        String cacheKey = createCacheKey(rule.getRuleNum(), "input_rule");
                        ruleDecisionCache.put(cacheKey, rule);
                        result.add(rule);
                        log.debug("Cached input rule {} with key {}", rule.getRuleNum(), cacheKey);
                    }
                }
            }

            log.info("Retrieved {} input rules ({} from cache, {} newly executed)",
                    result.size(), result.size() - missingRuleIds.size(), missingRuleIds.size());

            return result;

        } catch (Exception e) {
            log.error("Error in getInputRulesWithCache, falling back to direct execution", e);
            // Fallback to direct execution
            return cartRulesOrchestrationService.executeInputRulesFromCartResult(cartRulesDecision, inputRulesDroolVersion);
        }
    }

    /**
     * Get output rules with caching support
     * Checks cache first, then executes drool rules for missing entries
     */
    private List<OutputRulesDroolDecisionProperties> getOutputRulesWithCache(
            List<InputRulesDroolDecisionProperties> inputRules, String outputRulesDroolVersion) {

        List<OutputRulesDroolDecisionProperties> allOutputRules = new ArrayList<>();

        if (Objects.isNull(inputRules) || inputRules.isEmpty()) {
            return allOutputRules;
        }

        try {
            for (InputRulesDroolDecisionProperties inputRule : inputRules) {
                // Extract output rule IDs from input rule
                List<Integer> outputRuleIds = extractOutputRuleIds(inputRule);

                if (outputRuleIds.isEmpty()) {
                    // If no specific output rule IDs, execute the drool rule directly
                    List<OutputRulesDroolDecisionProperties> executedOutputRules = cartRulesOrchestrationService
                            .executeOutputRulesFromCartResult(inputRule, outputRulesDroolVersion);

                    // Cache the results
                    for (OutputRulesDroolDecisionProperties rule : executedOutputRules) {
                        if (Objects.nonNull(rule) && Objects.nonNull(rule.getRulesNumber())) {
                            String cacheKey = createCacheKey(rule.getRulesNumber(), "output_rule");
                            ruleDecisionCache.put(cacheKey, rule);
                            log.debug("Cached output rule {} with key {}", rule.getRulesNumber(), cacheKey);
                        }
                    }

                    allOutputRules.addAll(executedOutputRules);
                    continue;
                }

                List<OutputRulesDroolDecisionProperties> cachedOutputRules = new ArrayList<>();
                List<Integer> missingOutputRuleIds = new ArrayList<>();

                // Check cache for each output rule ID
                for (Integer ruleId : outputRuleIds) {
                    String cacheKey = createCacheKey(ruleId, "output_rule");
                    Object cachedRule = ruleDecisionCache.get(cacheKey);

                    if (cachedRule instanceof OutputRulesDroolDecisionProperties) {
                        cachedOutputRules.add((OutputRulesDroolDecisionProperties) cachedRule);
                        log.debug("Found output rule {} in cache", ruleId);
                    } else {
                        missingOutputRuleIds.add(ruleId);
                        log.debug("Output rule {} not found in cache", ruleId);
                    }
                }

                // Execute drool rules for missing output rules or if we need complete execution
                List<OutputRulesDroolDecisionProperties> executedOutputRules = cartRulesOrchestrationService
                        .executeOutputRulesFromCartResult(inputRule, outputRulesDroolVersion);

                // Cache the newly executed rules
                for (OutputRulesDroolDecisionProperties rule : executedOutputRules) {
                    if (Objects.nonNull(rule) && Objects.nonNull(rule.getRulesNumber())) {
                        String cacheKey = createCacheKey(rule.getRulesNumber(), "output_rule");
                        ruleDecisionCache.put(cacheKey, rule);
                        log.debug("Cached output rule {} with key {}", rule.getRulesNumber(), cacheKey);
                    }
                }

                // Add all executed rules to result (this ensures we get the complete drool execution result)
                allOutputRules.addAll(executedOutputRules);
            }

            log.info("Retrieved {} total output rules from all input rules", allOutputRules.size());
            return allOutputRules;

        } catch (Exception e) {
            log.error("Error in getOutputRulesWithCache, falling back to direct execution", e);
            // Fallback to direct execution
            for (InputRulesDroolDecisionProperties inputRule : inputRules) {
                List<OutputRulesDroolDecisionProperties> cartOutputRules = cartRulesOrchestrationService
                        .executeOutputRulesFromCartResult(inputRule, outputRulesDroolVersion);
                if (Objects.nonNull(cartOutputRules)) {
                    allOutputRules.addAll(cartOutputRules);
                }
            }
            return allOutputRules;
        }
    }

    /**
     * Create cache key in format: {ruleId}_{code}
     * @param ruleId the rule identifier
     * @param code the code type ("input_rule" or "output_rule")
     * @return formatted cache key
     */
    private String createCacheKey(Integer ruleId, String code) {
        return ruleId + "_" + code;
    }

    /**
     * Extract input rule IDs from cart rules decision
     * @param cartRulesDecision the cart rules decision properties
     * @return list of rule IDs
     */
    private List<Integer> extractInputRuleIds(CartRulesDroolDecisionProperties cartRulesDecision) {
        List<Integer> ruleIds = new ArrayList<>();

        if (Objects.nonNull(cartRulesDecision) && Objects.nonNull(cartRulesDecision.getInputRulesToBeRun())) {
            String[] ruleIdStrings = cartRulesDecision.getInputRulesToBeRun().split("_");
            for (String ruleIdString : ruleIdStrings) {
                try {
                    if (!ruleIdString.trim().isEmpty()) {
                        ruleIds.add(Integer.parseInt(ruleIdString.trim()));
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid input rule ID format: {}", ruleIdString);
                }
            }
        }

        return ruleIds;
    }

    /**
     * Extract output rule IDs from input rule
     * @param inputRule the input rule decision properties
     * @return list of output rule IDs
     */
    private List<Integer> extractOutputRuleIds(InputRulesDroolDecisionProperties inputRule) {
        List<Integer> ruleIds = new ArrayList<>();

        if (Objects.nonNull(inputRule) && Objects.nonNull(inputRule.getOutputRulesToBeRun())) {
            String[] ruleIdStrings = inputRule.getOutputRulesToBeRun().split("_");
            for (String ruleIdString : ruleIdStrings) {
                try {
                    if (!ruleIdString.trim().isEmpty()) {
                        ruleIds.add(Integer.parseInt(ruleIdString.trim()));
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid output rule ID format: {}", ruleIdString);
                }
            }
        }

        return ruleIds;
    }

    /**
     * Get cached input rule by ID
     * @param ruleId the rule identifier
     * @return cached input rule or null if not found
     */
    public InputRulesDroolDecisionProperties getCachedInputRule(Integer ruleId) {
        if (Objects.isNull(ruleId)) {
            return null;
        }

        String cacheKey = createCacheKey(ruleId, "input_rule");
        Object cachedRule = ruleDecisionCache.get(cacheKey);

        if (cachedRule instanceof InputRulesDroolDecisionProperties) {
            log.debug("Retrieved input rule {} from cache", ruleId);
            return (InputRulesDroolDecisionProperties) cachedRule;
        }

        return null;
    }

    /**
     * Get cached output rule by ID
     * @param ruleId the rule identifier
     * @return cached output rule or null if not found
     */
    public OutputRulesDroolDecisionProperties getCachedOutputRule(Integer ruleId) {
        if (Objects.isNull(ruleId)) {
            return null;
        }

        String cacheKey = createCacheKey(ruleId, "output_rule");
        Object cachedRule = ruleDecisionCache.get(cacheKey);

        if (cachedRule instanceof OutputRulesDroolDecisionProperties) {
            log.debug("Retrieved output rule {} from cache", ruleId);
            return (OutputRulesDroolDecisionProperties) cachedRule;
        }

        return null;
    }

    /**
     * Clear all cached rules
     */
    public void clearRuleDecisionCache() {
        int cacheSize = ruleDecisionCache.size();
        ruleDecisionCache.clear();
        log.info("Cleared rule decision cache, removed {} entries", cacheSize);
    }

    /**
     * Clear cached input rules only
     */
    public void clearInputRulesCache() {
        List<String> inputRuleKeys = ruleDecisionCache.keySet().stream()
                .filter(key -> key.endsWith("_input_rule"))
                .collect(Collectors.toList());

        inputRuleKeys.forEach(ruleDecisionCache::remove);
        log.info("Cleared {} input rule cache entries", inputRuleKeys.size());
    }

    /**
     * Clear cached output rules only
     */
    public void clearOutputRulesCache() {
        List<String> outputRuleKeys = ruleDecisionCache.keySet().stream()
                .filter(key -> key.endsWith("_output_rule"))
                .collect(Collectors.toList());

        outputRuleKeys.forEach(ruleDecisionCache::remove);
        log.info("Cleared {} output rule cache entries", outputRuleKeys.size());
    }

    /**
     * Get cache statistics
     * @return map containing cache statistics
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();

        long inputRuleCount = ruleDecisionCache.keySet().stream()
                .filter(key -> key.endsWith("_input_rule"))
                .count();

        long outputRuleCount = ruleDecisionCache.keySet().stream()
                .filter(key -> key.endsWith("_output_rule"))
                .count();

        stats.put("totalCacheEntries", ruleDecisionCache.size());
        stats.put("inputRuleEntries", inputRuleCount);
        stats.put("outputRuleEntries", outputRuleCount);

        return stats;
    }
}
